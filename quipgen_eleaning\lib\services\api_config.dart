class ApiConfig {
  // Base URL for the API
  static const String baseUrl = 'http://localhost:3000/api';

  // API endpoints
  static const String auth = '/auth';
  static const String user = '/user';
  static const String courses = '/courses';
  static const String lessons = '/lessons';
  static const String payment = '/payment';

  // Timeout configurations
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);

  // Headers
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Environment-specific configurations
  static String get environment {
    // You can change this based on build configuration
    return 'development'; // 'development', 'staging', 'production'
  }

  static String get apiBaseUrl {
    switch (environment) {
      case 'production':
        return 'https://your-production-api.com/api';
      case 'staging':
        return 'https://your-staging-api.com/api';
      case 'development':
      default:
        // Use ******** for Android emulator, localhost for iOS simulator
        return 'http://********:3001/api';
    }
  }

  // Platform-specific base URLs for development
  static String get androidEmulatorBaseUrl => 'http://********:3001/api';
  static String get iOSSimulatorBaseUrl => 'http://localhost:3001/api';
  static String get physicalDeviceBaseUrl =>
      'http://************:3001/api'; // Replace with your local IP

  // Full endpoint URLs
  static String get authUrl => '$apiBaseUrl$auth';
  static String get userUrl => '$apiBaseUrl$user';
  static String get coursesUrl => '$apiBaseUrl$courses';
  static String get lessonsUrl => '$apiBaseUrl$lessons';
  static String get paymentUrl => '$apiBaseUrl$payment';

  // Specific endpoints
  static String get profileUrl => '$authUrl/profile';
  static String get verifyTokenUrl => '$authUrl/verify';
  static String get enrollmentsUrl => '$userUrl/enrollments';
  static String get featuredCoursesUrl => '$coursesUrl/featured';
  static String get popularCoursesUrl => '$coursesUrl/popular';
  static String get categoriesUrl => '$coursesUrl/categories';
  static String get createOrderUrl => '$paymentUrl/create-order';
  static String get verifyPaymentUrl => '$paymentUrl/verify';
  static String get paymentHistoryUrl => '$paymentUrl/history';

  // Helper methods for dynamic URLs
  static String courseDetailsUrl(int courseId) => '$coursesUrl/$courseId';
  static String courseLessonsUrl(int courseId) =>
      '$lessonsUrl/course/$courseId';
  static String lessonDetailsUrl(int lessonId) => '$lessonsUrl/$lessonId';
  static String userProgressUrl(int courseId) => '$userUrl/progress/$courseId';
  static String updateProgressUrl(int courseId, int lessonId) =>
      '$userUrl/progress/$courseId/$lessonId';
  static String paymentStatusUrl(String orderId) =>
      '$paymentUrl/status/$orderId';
  static String lessonAccessUrl(int lessonId) => '$lessonsUrl/$lessonId/access';
  static String nextLessonUrl(int lessonId) => '$lessonsUrl/$lessonId/next';
}
