1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.quipgen.elearning"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:3:5-67
15-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:4:5-79
16-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:4:22-76
17    <!--
18 Required to query activities that can process text, see:
19         https://developer.android.com/training/package-visibility and
20         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
21
22         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
23    -->
24    <queries>
24-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:51:5-56:15
25        <intent>
25-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:52:9-55:18
26            <action android:name="android.intent.action.PROCESS_TEXT" />
26-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:53:13-72
26-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:53:21-70
27
28            <data android:mimeType="text/plain" />
28-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:54:13-50
28-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:54:19-48
29        </intent>
30        <intent>
30-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:11:9-17:18
31            <action android:name="android.intent.action.VIEW" />
31-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
31-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
32
33            <data
33-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:54:13-50
34                android:mimeType="*/*"
34-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:54:19-48
35                android:scheme="*" />
36        </intent>
37        <intent>
37-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:18:9-27:18
38            <action android:name="android.intent.action.VIEW" />
38-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
38-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
39
40            <category android:name="android.intent.category.BROWSABLE" />
40-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
40-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
41
42            <data
42-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:54:13-50
43                android:host="pay"
44                android:mimeType="*/*"
44-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:54:19-48
45                android:scheme="upi" />
46        </intent>
47        <intent>
47-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:28:9-30:18
48            <action android:name="android.intent.action.MAIN" />
48-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:29:13-65
48-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:29:21-62
49        </intent>
50        <intent>
50-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:31:9-35:18
51            <action android:name="android.intent.action.SEND" />
51-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:32:13-65
51-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:32:21-62
52
53            <data android:mimeType="*/*" />
53-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:54:13-50
53-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:54:19-48
54        </intent>
55        <intent>
55-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:36:9-38:18
56            <action android:name="rzp.device_token.share" />
56-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:37:13-61
56-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:37:21-58
57        </intent>
58    </queries>
59
60    <uses-permission android:name="android.permission.WAKE_LOCK" />
60-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:24:5-68
60-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:24:22-65
61    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
61-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:5-79
61-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:22-76
62    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
62-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:5-88
62-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:22-85
63    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
63-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:5-82
63-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:22-79
64    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
64-->[com.google.android.recaptcha:recaptcha:18.4.0] D:\Gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
64-->[com.google.android.recaptcha:recaptcha:18.4.0] D:\Gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
65
66    <uses-feature
66-->[com.google.android.gms:play-services-maps:17.0.0] D:\Gradle\caches\8.12\transforms\e7870f097196d2c7d6d68eb7fdda7e77\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:26:5-28:35
67        android:glEsVersion="0x00020000"
67-->[com.google.android.gms:play-services-maps:17.0.0] D:\Gradle\caches\8.12\transforms\e7870f097196d2c7d6d68eb7fdda7e77\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:27:9-41
68        android:required="true" />
68-->[com.google.android.gms:play-services-maps:17.0.0] D:\Gradle\caches\8.12\transforms\e7870f097196d2c7d6d68eb7fdda7e77\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:28:9-32
69
70    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
70-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:5-110
70-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:22-107
71
72    <permission
72-->[androidx.core:core:1.13.1] D:\Gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
73        android:name="com.quipgen.elearning.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
73-->[androidx.core:core:1.13.1] D:\Gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
74        android:protectionLevel="signature" />
74-->[androidx.core:core:1.13.1] D:\Gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
75
76    <uses-permission android:name="com.quipgen.elearning.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
76-->[androidx.core:core:1.13.1] D:\Gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
76-->[androidx.core:core:1.13.1] D:\Gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
77
78    <application
79        android:name="android.app.Application"
80        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
80-->[androidx.core:core:1.13.1] D:\Gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
81        android:debuggable="true"
82        android:extractNativeLibs="true"
83        android:icon="@mipmap/ic_launcher"
84        android:label="quipgen_eleaning"
85        android:usesCleartextTraffic="true" >
86        <activity
87            android:name="com.quipgen.elearning.MainActivity"
88            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
89            android:exported="true"
90            android:hardwareAccelerated="true"
91            android:launchMode="singleTop"
92            android:taskAffinity=""
93            android:theme="@style/LaunchTheme"
94            android:windowSoftInputMode="adjustResize" >
95
96            <!--
97                 Specifies an Android theme to apply to this Activity as soon as
98                 the Android process has started. This theme is visible to the user
99                 while the Flutter UI initializes. After that, this theme continues
100                 to determine the Window background behind the Flutter UI.
101            -->
102            <meta-data
103                android:name="io.flutter.embedding.android.NormalTheme"
104                android:resource="@style/NormalTheme" />
105
106            <intent-filter>
107                <action android:name="android.intent.action.MAIN" />
107-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:29:13-65
107-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:29:21-62
108
109                <category android:name="android.intent.category.LAUNCHER" />
110            </intent-filter>
111        </activity>
112        <!--
113             Don't delete the meta-data below.
114             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
115        -->
116        <meta-data
117            android:name="flutterEmbedding"
118            android:value="2" />
119
120        <!-- Disable automatic Firebase initialization -->
121        <provider
122            android:name="com.google.firebase.provider.FirebaseInitProvider"
123            android:authorities="com.quipgen.elearning.firebaseinitprovider"
124            android:directBootAware="true"
124-->[com.google.firebase:firebase-common:20.4.3] D:\Gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
125            android:enabled="false"
126            android:exported="false"
127            android:initOrder="100" />
127-->[com.google.firebase:firebase-common:20.4.3] D:\Gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
128
129        <service
129-->[:firebase_auth] D:\Task_Project\quipgen_eleaning\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
130            android:name="com.google.firebase.components.ComponentDiscoveryService"
130-->[:firebase_auth] D:\Task_Project\quipgen_eleaning\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
131            android:directBootAware="true"
131-->[com.google.firebase:firebase-common:20.4.3] D:\Gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
132            android:exported="false" >
132-->[com.google.firebase:firebase-auth-ktx:22.3.1] D:\Gradle\caches\8.12\transforms\1b00888fe10f74f3e7b67235eb6c8a60\transformed\jetified-firebase-auth-ktx-22.3.1\AndroidManifest.xml:10:13-37
133            <meta-data
133-->[:firebase_auth] D:\Task_Project\quipgen_eleaning\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
134                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
134-->[:firebase_auth] D:\Task_Project\quipgen_eleaning\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[:firebase_auth] D:\Task_Project\quipgen_eleaning\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
136            <meta-data
136-->[:firebase_core] D:\Task_Project\quipgen_eleaning\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
137                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
137-->[:firebase_core] D:\Task_Project\quipgen_eleaning\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[:firebase_core] D:\Task_Project\quipgen_eleaning\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
139            <meta-data
139-->[com.google.firebase:firebase-auth-ktx:22.3.1] D:\Gradle\caches\8.12\transforms\1b00888fe10f74f3e7b67235eb6c8a60\transformed\jetified-firebase-auth-ktx-22.3.1\AndroidManifest.xml:11:13-13:85
140                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
140-->[com.google.firebase:firebase-auth-ktx:22.3.1] D:\Gradle\caches\8.12\transforms\1b00888fe10f74f3e7b67235eb6c8a60\transformed\jetified-firebase-auth-ktx-22.3.1\AndroidManifest.xml:12:17-119
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-auth-ktx:22.3.1] D:\Gradle\caches\8.12\transforms\1b00888fe10f74f3e7b67235eb6c8a60\transformed\jetified-firebase-auth-ktx-22.3.1\AndroidManifest.xml:13:17-82
142            <meta-data
142-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
143                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
143-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
145            <meta-data
145-->[com.google.firebase:firebase-analytics-ktx:21.6.1] D:\Gradle\caches\8.12\transforms\1c7efed489efea967d9fe7b470ea80e1\transformed\jetified-firebase-analytics-ktx-21.6.1\AndroidManifest.xml:11:13-13:85
146                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
146-->[com.google.firebase:firebase-analytics-ktx:21.6.1] D:\Gradle\caches\8.12\transforms\1c7efed489efea967d9fe7b470ea80e1\transformed\jetified-firebase-analytics-ktx-21.6.1\AndroidManifest.xml:12:17-129
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-analytics-ktx:21.6.1] D:\Gradle\caches\8.12\transforms\1c7efed489efea967d9fe7b470ea80e1\transformed\jetified-firebase-analytics-ktx-21.6.1\AndroidManifest.xml:13:17-82
148            <meta-data
148-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:37:13-39:85
149                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
149-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:38:17-139
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:39:17-82
151            <meta-data
151-->[com.google.firebase:firebase-installations:17.2.0] D:\Gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
152                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
152-->[com.google.firebase:firebase-installations:17.2.0] D:\Gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-installations:17.2.0] D:\Gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
154            <meta-data
154-->[com.google.firebase:firebase-installations:17.2.0] D:\Gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
155                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
155-->[com.google.firebase:firebase-installations:17.2.0] D:\Gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-installations:17.2.0] D:\Gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
157            <meta-data
157-->[com.google.firebase:firebase-common-ktx:20.4.3] D:\Gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
158                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
158-->[com.google.firebase:firebase-common-ktx:20.4.3] D:\Gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
159                android:value="com.google.firebase.components.ComponentRegistrar" />
159-->[com.google.firebase:firebase-common-ktx:20.4.3] D:\Gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
160            <meta-data
160-->[com.google.firebase:firebase-common:20.4.3] D:\Gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
161                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
161-->[com.google.firebase:firebase-common:20.4.3] D:\Gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
162                android:value="com.google.firebase.components.ComponentRegistrar" />
162-->[com.google.firebase:firebase-common:20.4.3] D:\Gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
163        </service>
164
165        <activity
165-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
166            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
166-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
167            android:excludeFromRecents="true"
167-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
168            android:exported="true"
168-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
169            android:launchMode="singleTask"
169-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
170            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
170-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
171            <intent-filter>
171-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
172                <action android:name="android.intent.action.VIEW" />
172-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
172-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
173
174                <category android:name="android.intent.category.DEFAULT" />
174-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
174-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
175                <category android:name="android.intent.category.BROWSABLE" />
175-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
175-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
176
177                <data
177-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:54:13-50
178                    android:host="firebase.auth"
179                    android:path="/"
180                    android:scheme="genericidp" />
181            </intent-filter>
182        </activity>
183        <activity
183-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
184            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
184-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
185            android:excludeFromRecents="true"
185-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
186            android:exported="true"
186-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
187            android:launchMode="singleTask"
187-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
188            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
188-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
189            <intent-filter>
189-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
190                <action android:name="android.intent.action.VIEW" />
190-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
190-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
191
192                <category android:name="android.intent.category.DEFAULT" />
192-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
192-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
193                <category android:name="android.intent.category.BROWSABLE" />
193-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
193-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
194
195                <data
195-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:54:13-50
196                    android:host="firebase.auth"
197                    android:path="/"
198                    android:scheme="recaptcha" />
199            </intent-filter>
200        </activity>
201        <activity
201-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:42:9-50:20
202            android:name="com.razorpay.CheckoutActivity"
202-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:43:13-57
203            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
203-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:44:13-83
204            android:exported="false"
204-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:45:13-37
205            android:theme="@style/CheckoutTheme" >
205-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:46:13-49
206            <intent-filter>
206-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:47:13-49:29
207                <action android:name="android.intent.action.MAIN" />
207-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:29:13-65
207-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:29:21-62
208            </intent-filter>
209        </activity>
210
211        <provider
211-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:52:9-60:20
212            android:name="androidx.startup.InitializationProvider"
212-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:53:13-67
213            android:authorities="com.quipgen.elearning.androidx-startup"
213-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:54:13-68
214            android:exported="false" >
214-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:55:13-37
215            <meta-data
215-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:57:13-59:52
216                android:name="com.razorpay.RazorpayInitializer"
216-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:58:17-64
217                android:value="androidx.startup" />
217-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:59:17-49
218            <meta-data
218-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
219                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
219-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
220                android:value="androidx.startup" />
220-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
221            <meta-data
221-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
222                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
222-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
223                android:value="androidx.startup" />
223-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
224        </provider>
225
226        <activity
226-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:62:9-65:75
227            android:name="com.razorpay.MagicXActivity"
227-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:63:13-55
228            android:exported="false"
228-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:64:13-37
229            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
229-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:65:13-72
230
231        <meta-data
231-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:67:9-69:58
232            android:name="com.razorpay.plugin.googlepay_all"
232-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:68:13-61
233            android:value="com.razorpay.RzpGpayMerged" />
233-->[com.razorpay:standard-core:1.6.53] D:\Gradle\caches\8.12\transforms\9101bd03947c9925ecdc640cffd04b31\transformed\jetified-standard-core-1.6.53\AndroidManifest.xml:69:13-55
234
235        <activity
235-->[com.google.android.gms:play-services-auth:21.1.0] D:\Gradle\caches\8.12\transforms\9f25f32b34ee349c625687ef81bee590\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
236            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
236-->[com.google.android.gms:play-services-auth:21.1.0] D:\Gradle\caches\8.12\transforms\9f25f32b34ee349c625687ef81bee590\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
237            android:excludeFromRecents="true"
237-->[com.google.android.gms:play-services-auth:21.1.0] D:\Gradle\caches\8.12\transforms\9f25f32b34ee349c625687ef81bee590\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
238            android:exported="false"
238-->[com.google.android.gms:play-services-auth:21.1.0] D:\Gradle\caches\8.12\transforms\9f25f32b34ee349c625687ef81bee590\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
239            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
239-->[com.google.android.gms:play-services-auth:21.1.0] D:\Gradle\caches\8.12\transforms\9f25f32b34ee349c625687ef81bee590\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
240        <!--
241            Service handling Google Sign-In user revocation. For apps that do not integrate with
242            Google Sign-In, this service will never be started.
243        -->
244        <service
244-->[com.google.android.gms:play-services-auth:21.1.0] D:\Gradle\caches\8.12\transforms\9f25f32b34ee349c625687ef81bee590\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
245            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
245-->[com.google.android.gms:play-services-auth:21.1.0] D:\Gradle\caches\8.12\transforms\9f25f32b34ee349c625687ef81bee590\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
246            android:exported="true"
246-->[com.google.android.gms:play-services-auth:21.1.0] D:\Gradle\caches\8.12\transforms\9f25f32b34ee349c625687ef81bee590\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
247            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
247-->[com.google.android.gms:play-services-auth:21.1.0] D:\Gradle\caches\8.12\transforms\9f25f32b34ee349c625687ef81bee590\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
248            android:visibleToInstantApps="true" />
248-->[com.google.android.gms:play-services-auth:21.1.0] D:\Gradle\caches\8.12\transforms\9f25f32b34ee349c625687ef81bee590\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
249
250        <property
250-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:30:9-32:61
251            android:name="android.adservices.AD_SERVICES_CONFIG"
251-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:31:13-65
252            android:resource="@xml/ga_ad_services_config" /> <!-- Needs to be explicitly declared on P+ -->
252-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:32:13-58
253        <uses-library
253-->[com.google.android.gms:play-services-maps:17.0.0] D:\Gradle\caches\8.12\transforms\e7870f097196d2c7d6d68eb7fdda7e77\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:33:9-35:40
254            android:name="org.apache.http.legacy"
254-->[com.google.android.gms:play-services-maps:17.0.0] D:\Gradle\caches\8.12\transforms\e7870f097196d2c7d6d68eb7fdda7e77\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:34:13-50
255            android:required="false" />
255-->[com.google.android.gms:play-services-maps:17.0.0] D:\Gradle\caches\8.12\transforms\e7870f097196d2c7d6d68eb7fdda7e77\transformed\jetified-play-services-maps-17.0.0\AndroidManifest.xml:35:13-37
256
257        <activity
257-->[com.google.android.gms:play-services-base:18.3.0] D:\Gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
258            android:name="com.google.android.gms.common.api.GoogleApiActivity"
258-->[com.google.android.gms:play-services-base:18.3.0] D:\Gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
259            android:exported="false"
259-->[com.google.android.gms:play-services-base:18.3.0] D:\Gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
260            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
260-->[com.google.android.gms:play-services-base:18.3.0] D:\Gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
261
262        <receiver
262-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:29:9-33:20
263            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
263-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:30:13-85
264            android:enabled="true"
264-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:31:13-35
265            android:exported="false" >
265-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:32:13-37
266        </receiver>
267
268        <service
268-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:35:9-38:40
269            android:name="com.google.android.gms.measurement.AppMeasurementService"
269-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:36:13-84
270            android:enabled="true"
270-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:37:13-35
271            android:exported="false" />
271-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:38:13-37
272        <service
272-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:39:9-43:72
273            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
273-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:40:13-87
274            android:enabled="true"
274-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:41:13-35
275            android:exported="false"
275-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:42:13-37
276            android:permission="android.permission.BIND_JOB_SERVICE" />
276-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:43:13-69
277
278        <uses-library
278-->[androidx.window:window:1.2.0] D:\Gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
279            android:name="androidx.window.extensions"
279-->[androidx.window:window:1.2.0] D:\Gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
280            android:required="false" />
280-->[androidx.window:window:1.2.0] D:\Gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
281        <uses-library
281-->[androidx.window:window:1.2.0] D:\Gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
282            android:name="androidx.window.sidecar"
282-->[androidx.window:window:1.2.0] D:\Gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
283            android:required="false" />
283-->[androidx.window:window:1.2.0] D:\Gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
284        <uses-library
284-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] D:\Gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
285            android:name="android.ext.adservices"
285-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] D:\Gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
286            android:required="false" />
286-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] D:\Gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
287
288        <meta-data
288-->[com.google.android.gms:play-services-basement:18.3.0] D:\Gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
289            android:name="com.google.android.gms.version"
289-->[com.google.android.gms:play-services-basement:18.3.0] D:\Gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
290            android:value="@integer/google_play_services_version" />
290-->[com.google.android.gms:play-services-basement:18.3.0] D:\Gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
291
292        <receiver
292-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
293            android:name="androidx.profileinstaller.ProfileInstallReceiver"
293-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
294            android:directBootAware="false"
294-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
295            android:enabled="true"
295-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
296            android:exported="true"
296-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
297            android:permission="android.permission.DUMP" >
297-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
298            <intent-filter>
298-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
299                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
299-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
299-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
300            </intent-filter>
301            <intent-filter>
301-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
302                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
302-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
302-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
303            </intent-filter>
304            <intent-filter>
304-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
305                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
305-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
305-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
306            </intent-filter>
307            <intent-filter>
307-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
308                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
308-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
308-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
309            </intent-filter>
310        </receiver>
311    </application>
312
313</manifest>
